package main

import (
	"log"

	"github.com/gin-gonic/gin"
	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/dao"
	"github.com/jony4/52kanduanju.mp/server/middleware"
	"github.com/jony4/52kanduanju.mp/server/routes"
)

func main() {
	// Load configuration
	cfg := config.LoadConfig()

	// Initialize JWT
	middleware.InitJWT(cfg)

	// Set Gin mode
	gin.SetMode(cfg.Server.Mode)

	// Initialize database
	if err := dao.InitDatabase(cfg); err != nil {
		log.Fatal("Failed to connect to database:", err)
	}
	defer dao.CloseDatabase()

	// Create Gin router
	router := gin.New()

	// Setup routes with dependencies
	routes.SetupRoutes(router, dao.GetDB(), cfg)

	// Start server
	if err := router.Run(":" + cfg.Server.Port); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
