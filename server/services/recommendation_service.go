package services

import (
	"time"

	"github.com/jony4/52kanduanju.mp/server/config"
	"github.com/jony4/52kanduanju.mp/server/dao"
	"github.com/jony4/52kanduanju.mp/server/models"
	"gorm.io/gorm"
)

type RecommendationService struct {
	*BaseService
	duanjuDAO       *dao.DuanjuDAO
	actorDAO        *dao.DuanjuActorDAO
	dailyFortuneDAO *dao.DailyFortuneDAO
}

func NewRecommendationService(db *gorm.DB, cfg *config.Config) *RecommendationService {
	return &RecommendationService{
		BaseService:     NewBaseService(db, cfg),
		duanjuDAO:       dao.NewDuanjuDAO(db),
		actorDAO:        dao.NewDuanjuActorDAO(db),
		dailyFortuneDAO: dao.NewDailyFortuneDAO(db),
	}
}

// GetHomeRecommendation 获取首页推荐内容
func (s *RecommendationService) GetHomeRecommendation() (*models.HomeRecommendationData, error) {
	// 获取今日运势
	todayFortune, err := s.getTodayFortune()
	if err != nil {
		// 如果获取失败，使用默认运势
		todayFortune = &models.TodayFortuneData{
			Date: time.Now().Format("01/02"),
			Good: "看甜甜的恋爱",
			Bad:  "沉溺于痛苦",
		}
	}

	// 获取热门短剧
	hotDuanjus, err := s.duanjuDAO.GetHotDuanjus(12)
	if err != nil {
		return nil, err
	}

	dramas := make([]*models.DuanjuResponse, 0, len(hotDuanjus))
	for _, duanju := range hotDuanjus {
		duanjuResp := s.ConvertDuanjuToResponse(&duanju)
		dramas = append(dramas, duanjuResp)
	}

	return &models.HomeRecommendationData{
		TodayFortune: *todayFortune,
		Dramas:       dramas,
	}, nil
}

// getTodayFortune 获取今日运势
func (s *RecommendationService) getTodayFortune() (*models.TodayFortuneData, error) {
	fortune, err := s.dailyFortuneDAO.GetTodayFortune()
	if err != nil {
		return nil, err
	}

	var dateStr string
	if fortune.Today != nil {
		dateStr = fortune.Today.Format("01/02")
	} else {
		dateStr = time.Now().Format("01/02")
	}

	var goodText string
	if fortune.GoodText != nil {
		goodText = *fortune.GoodText
	}

	var badText string
	if fortune.BadText != nil {
		badText = *fortune.BadText
	}

	return &models.TodayFortuneData{
		Date: dateStr,
		Good: goodText,
		Bad:  badText,
	}, nil
}
